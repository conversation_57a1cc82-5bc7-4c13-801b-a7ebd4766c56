/* Statistics Dashboard Styles */
.statistics-dashboard {
    background: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Page-specific styles */
.statistics-dashboard.page-dashboard {
    box-shadow: none;
    border: none;
    background: transparent;
}

.dashboard-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 4px 4px 0 0;
}

/* Page-specific header styles */
.statistics-dashboard.page-dashboard .dashboard-header {
    background: transparent;
    border-bottom: 1px solid #ddd;
    padding: 20px 0;
}

.dashboard-header h4 {
    margin: 0;
    color: #333;
    font-size: 16px;
    font-weight: 500;
}

.dashboard-header h4 i {
    margin-right: 8px;
    color: #2196F3;
}

.dashboard-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-controls select {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 13px;
}

.dashboard-controls .btn {
    height: 32px;
    padding: 4px 12px;
    font-size: 13px;
    line-height: 1.4;
}

.dashboard-content {
    padding: 20px;
    min-height: 300px;
    position: relative;
}

#stats-chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

#stats-chart {
    max-height: 300px;
}

#stats-loading {
    color: #666;
    font-size: 14px;
}

#stats-loading .spinner {
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#stats-error {
    margin: 0;
    font-size: 14px;
}

#stats-no-data {
    color: #999;
    font-size: 14px;
}



/* Responsive design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dashboard-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    

    
    .dashboard-controls select {
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    .dashboard-content {
        padding: 15px;
    }
    
    .dashboard-header {
        padding: 12px 15px;
    }
    
    .dashboard-controls {
        gap: 8px;
    }
    
    .dashboard-controls select,
    .dashboard-controls .btn {
        font-size: 12px;
        height: 30px;
    }
}



/* Loading state improvements */
.dashboard-content.loading {
    pointer-events: none;
}

.dashboard-content.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 10;
}

/* Chart container improvements */
#stats-chart-container canvas {
    border-radius: 4px;
}

/* Better error styling */
#stats-error {
    border-left: 4px solid #f44336;
    background: #ffebee;
    border-color: #ffcdd2;
}

/* Better no-data styling */
#stats-no-data {
    background: #f5f5f5;
    border-radius: 4px;
    border: 1px dashed #ccc;
}

#stats-no-data i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}
