/**
 * Statistics Dashboard Component
 * Displays procedure statistics by user with month/year selector
 */
class StatisticsDashboard {
    constructor(containerId, apiEndpoint, options = {}) {
        this.containerId = containerId;
        this.apiEndpoint = apiEndpoint;
        this.chart = null;
        this.currentMonth = new Date().getMonth() + 1;
        this.currentYear = new Date().getFullYear();
        this.options = options;
        this.onDataLoaded = null; // Callback for when data is loaded

        this.init();
    }
    
    init() {
        this.createHTML();
        this.bindEvents();
        this.loadData();
    }
    
    createHTML() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error('Statistics dashboard container not found:', this.containerId);
            return;
        }
        
        container.innerHTML = `
            <div class="statistics-dashboard ${this.options.pageMode ? 'page-dashboard' : ''}"
                <div class="dashboard-header">
                    <h4><i class="icon-stats-bars"></i> Statistiche Pratiche</h4>
                    <div class="dashboard-controls">
                        <select id="stats-month" class="form-control" style="width: auto; display: inline-block; margin-right: 10px;">
                            <option value="1">Gennaio</option>
                            <option value="2">Febbraio</option>
                            <option value="3">Marzo</option>
                            <option value="4">Aprile</option>
                            <option value="5">Maggio</option>
                            <option value="6">Giugno</option>
                            <option value="7">Luglio</option>
                            <option value="8">Agosto</option>
                            <option value="9">Settembre</option>
                            <option value="10">Ottobre</option>
                            <option value="11">Novembre</option>
                            <option value="12">Dicembre</option>
                        </select>
                        <select id="stats-year" class="form-control" style="width: auto; display: inline-block;">
                            ${this.generateYearOptions()}
                        </select>
                        <button id="stats-refresh" class="btn btn-primary btn-sm" style="margin-left: 10px;">
                            <i class="icon-reload-alt"></i> Aggiorna
                        </button>
                    </div>
                </div>
                <div class="dashboard-content">
                    <div id="stats-loading" class="text-center" style="padding: 20px;">
                        <i class="icon-spinner2 spinner"></i> Caricamento...
                    </div>
                    <div id="stats-error" class="alert alert-danger" style="display: none;">
                        <i class="icon-warning"></i> Errore nel caricamento delle statistiche.
                    </div>
                    <div id="stats-chart-container" style="display: none;">
                        <canvas id="stats-chart" width="400" height="200"></canvas>
                    </div>
                    <div id="stats-no-data" class="text-center text-muted" style="display: none; padding: 20px;">
                        <i class="icon-info"></i> Nessuna pratica trovata per il periodo selezionato.
                    </div>
                </div>
            </div>
        `;
        
        // Set current month and year
        document.getElementById('stats-month').value = this.currentMonth;
        document.getElementById('stats-year').value = this.currentYear;
    }
    
    generateYearOptions() {
        const currentYear = new Date().getFullYear();
        let options = '';
        for (let year = currentYear - 5; year <= currentYear + 1; year++) {
            options += `<option value="${year}">${year}</option>`;
        }
        return options;
    }
    
    bindEvents() {
        const refreshBtn = document.getElementById('stats-refresh');
        const monthSelect = document.getElementById('stats-month');
        const yearSelect = document.getElementById('stats-year');
        
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.currentMonth = parseInt(monthSelect.value);
                this.currentYear = parseInt(yearSelect.value);
                this.loadData();
            });
        }
        
        // Auto-refresh on select change
        if (monthSelect) {
            monthSelect.addEventListener('change', () => {
                this.currentMonth = parseInt(monthSelect.value);
                this.loadData();
            });
        }
        
        if (yearSelect) {
            yearSelect.addEventListener('change', () => {
                this.currentYear = parseInt(yearSelect.value);
                this.loadData();
            });
        }
    }
    
    loadData() {
        this.showLoading();
        
        const url = `${this.apiEndpoint}?month=${this.currentMonth}&year=${this.currentYear}`;
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                this.hideLoading();
                this.renderChart(data);

                // Call callback if provided
                if (this.onDataLoaded && typeof this.onDataLoaded === 'function') {
                    this.onDataLoaded(data);
                }
            })
            .catch(error => {
                console.error('Error loading statistics:', error);
                this.hideLoading();
                this.showError();
            });
    }
    
    showLoading() {
        document.getElementById('stats-loading').style.display = 'block';
        document.getElementById('stats-error').style.display = 'none';
        document.getElementById('stats-chart-container').style.display = 'none';
        document.getElementById('stats-no-data').style.display = 'none';
    }
    
    hideLoading() {
        document.getElementById('stats-loading').style.display = 'none';
    }
    
    showError() {
        document.getElementById('stats-error').style.display = 'block';
    }
    
    renderChart(data) {
        if (!data.statistics || data.statistics.length === 0) {
            document.getElementById('stats-no-data').style.display = 'block';
            return;
        }
        
        document.getElementById('stats-chart-container').style.display = 'block';
        
        // Prepare chart data
        const labels = [];
        const counts = [];
        const colors = [
            '#4CAF50', '#2196F3', '#FF9800', '#F44336', '#9C27B0', '#00BCD4',
            '#8BC34A', '#FFC107', '#E91E63', '#607D8B', '#795548', '#FF5722'
        ];
        
        data.statistics.forEach((stat, index) => {
            const userId = stat.userId;
            const userName = data.userNames[userId] || 'Utente sconosciuto';
            labels.push(userName);
            counts.push(stat.count);
        });
        
        // Destroy existing chart if it exists
        if (this.chart) {
            this.chart.destroy();
        }
        
        // Create new chart
        const ctx = document.getElementById('stats-chart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Numero Pratiche',
                    data: counts,
                    backgroundColor: colors.slice(0, labels.length),
                    borderColor: colors.slice(0, labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `Pratiche per Utente - ${this.getMonthName(this.currentMonth)} ${this.currentYear}`
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
    
    getMonthName(month) {
        const months = [
            'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
            'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
        ];
        return months[month - 1] || 'Mese sconosciuto';
    }
    
    destroy() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }
}

// Export for use in other scripts
window.StatisticsDashboard = StatisticsDashboard;
