package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.ProcedureNote;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.bson.types.ObjectId;

import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;

/**
 * DAO for procedure notes operations
 * 
 * <AUTHOR> Augster
 */
public class ProcedureNoteDao {
    
    public static ProcedureNote loadProcedureNote(ObjectId noteId) throws Exception {
        if (noteId == null) {
            throw new InvalidParameterException("empty noteId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedurenote");
        Document doc = collection.find(eq("_id", noteId)).first();
        return Manager.fromDocument(doc, ProcedureNote.class);
    }
    
    public static List<ProcedureNote> loadProcedureNotesByProcedureId(ObjectId procedureId) throws Exception {
        if (procedureId == null) {
            throw new InvalidParameterException("empty procedureId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedurenote");
        FindIterable<Document> list = collection
                .find(eq("procedureId", procedureId))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, ProcedureNote.class);
    }
    
    public static ObjectId insertProcedureNote(ProcedureNote note) throws Exception {
        if (note == null) {
            throw new InvalidParameterException("empty note");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        note.setCreation(now);
        note.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedurenote");
        Document doc = Manager.toDocument(note);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }
    
    public static void updateProcedureNote(ProcedureNote note) throws Exception {
        if (note == null) {
            throw new InvalidParameterException("empty note");
        }

        // defaults
        Date now = new Date();
        
        // internals
        note.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedurenote");
        collection.replaceOne(
                new Document("_id", note.getId()),
                Manager.toDocument(note)
        );
    }
    
    public static void deleteProcedureNote(ObjectId noteId) throws Exception {
        if (noteId == null) {
            throw new InvalidParameterException("empty noteId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedurenote");
        collection.deleteOne(eq("_id", noteId));
    }
}
