package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.util.MongoUtils;
import com.miocontotermico.util.TimeUtils;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.security.InvalidParameterException;
import java.util.*;

import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.*;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;

/**
 *
 * <AUTHOR>
 */
public class ProcedureDao {

    public static Procedure loadProcedure(ObjectId procedureId) throws Exception {
        if (procedureId == null) {
            throw new InvalidParameterException("empty procedureId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        Document doc = collection.find(eq("_id", procedureId)).first();
        return Manager.fromDocument(doc, Procedure.class);
    }

    public static Procedure loadDraftProcedure(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("status", StatusType.draft.toString()),
                        eq("userId", userId)))
                .first();
        return Manager.fromDocument(doc, Procedure.class);
    }

    public static List<Procedure> loadProcedureListByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));

        filters.add(eq("userId", userId));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        FindIterable<Document> list = collection
                .find(and(filters));
        
        return Manager.fromDocumentList(list, Procedure.class);
    }
    
    public static List<Procedure> loadProcedureListByDateRangeAndStatus(Date from, Date to, String[] statuses, ObjectId userId, ObjectId assignedUserId) throws Exception {
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(ne("status", StatusType.draft.toString()));
     
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("date", from));
        }
        
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("date", to));
        }
        
        if (statuses != null) {
            filters.add(in("status", statuses));
        }
        
        if (userId != null) {
            filters.add(in("userId", userId));
        }
        if (assignedUserId != null) {
            filters.add(in("assignedUserId", assignedUserId));
        }
        
        Bson sort = orderBy(ascending("date"), ascending("_id"));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        FindIterable<Document> list = collection
                .find(and(filters))
                .limit(10000)
                .sort(sort);
        
        return Manager.fromDocumentList(list, Procedure.class);
    }
    
    public static ObjectId insertProcedure(Procedure procedure) throws Exception {
        if (procedure == null) {
            throw new InvalidParameterException("empty procedure");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        procedure.setCreation(now);
        procedure.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        Document doc = Manager.toDocument(procedure);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateProcedure(Procedure procedure) throws Exception {
        if (procedure == null) {
            throw new InvalidParameterException("empty procedure");
        }

        // defaults
        Date now = new Date();
        
        // internals
        procedure.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        collection.replaceOne(
                new Document("_id", procedure.getId()),
                Manager.toDocument(procedure)
        );
        
    }

    public static void updateProcedureCancelled(ObjectId procedureId, boolean cancelled) throws Exception {
        if (procedureId == null) {
            throw new InvalidParameterException("empty procedureId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Procedure procedure = loadProcedure(procedureId);
        procedure.setCancelled(cancelled);
        
        // internals
        procedure.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        collection.replaceOne(
                new Document("_id", procedure.getId()),
                Manager.toDocument(procedure)
        );
        
    }

    public static void removeDraftProcedure(ObjectId procedureId) throws Exception {
        if (procedureId == null) {
            throw new InvalidParameterException("empty procedureId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Procedure procedure = loadProcedure(procedureId);
        procedure.setCancelled(true);
        
        // internals
        procedure.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        collection.replaceOne(
                new Document("_id", procedure.getId()),
                Manager.toDocument(procedure)
        );
    }
    
    public static List<StatusEntry> loadProcedureStatusList() throws Exception {
        /*
            db.getCollection('procedure').aggregate(
                [
                    {
                        $match: {
                            status: {$ne: '', $ne: null}
                        }
                    },
                    {
                        $group: {
                            _id : '$status',
                            status: { $min: '$status'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, status: 1}},
                    { $sort : { stato : 1 } }
                ]
            )
        */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(
                            ne("status", ""),
                            ne("status", "draft"),
                            ne("status", null)
                    )),
                    group("$status", min("status", "$status"), sum("count", "1")),
                    project(fields(excludeId(), include("status"))),
                    sort(orderBy(ascending("status")))
                ));
        return Manager.fromAggregateList(list, StatusEntry.class);
    }

    public static List<Map<String, Object>> loadProcedureStatisticsByUser(Date from, Date to) throws Exception {
        /*
            db.getCollection('procedure').aggregate([
                {
                    $match: {
                        cancelled: {$ne: true},
                        status: {$ne: 'draft'},
                        date: {$gte: from, $lte: to}
                    }
                },
                {
                    $group: {
                        _id: '$assignedUserId',
                        count: {$sum: 1}
                    }
                },
                {
                    $project: {
                        _id: 0,
                        userId: '$_id',
                        count: 1
                    }
                }
            ])
        */

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("status", StatusType.draft.toString()));

        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("date", from));
        }

        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("date", to));
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("procedure");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(filters)),
                    group("$assignedUserId", sum("count", 1)),
                    project(fields(excludeId(), include("count"), computed("userId", "$_id")))
                ));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Document doc : list) {
            Map<String, Object> stat = new HashMap<>();
            stat.put("userId", doc.get("userId"));
            stat.put("count", doc.get("count"));
            result.add(stat);
        }

        return result;
    }
}
