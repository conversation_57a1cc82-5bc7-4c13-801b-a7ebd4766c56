package com.miocontotermico.data;

import com.miocontotermico.core.Manager;
import com.miocontotermico.dao.CityDao;
import com.miocontotermico.dao.ImageDao;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.City;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.support.image.limitless.Image;
import com.miocontotermico.util.ImageUtils;
import com.miocontotermico.util.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;

import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataController.class.getName());

    public static Route data_cities = (Request request, Response response) -> {

        String name = request.queryParams("name");

        String stringToRemove = StringUtils.substringBetween(name, "(", ")");
        if (StringUtils.isNotBlank(stringToRemove)) {
            name = StringUtils.remove(name, "(" + stringToRemove + ")");
        }
        name = StringUtils.remove(name, "(");
        name = StringUtils.remove(name, ")");
        name = StringUtils.remove(name, "^");
        name = StringUtils.remove(name, "$");

        List<City> cities = CityDao.loadCityList(name);

        //description

        String[][] names;
        if ((cities != null) && (cities.size() > 0)) {
            names = new String[cities.size()][3];
            for (int i = 0; i < cities.size(); i++) {
                names[i][0] = cities.get(i).getCity();
                names[i][1] = cities.get(i).getPostalCode() != null ? cities.get(i).getPostalCode() : "";
                names[i][2] = cities.get(i).getProvinceCode() != null ? cities.get(i).getProvinceCode() : "";
            }
        } else {
            names = new String[0][0];
        }

        return names;
    };

    public static Route data_statistics_procedures_by_user = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // Check if user is admin or operator
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
            !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
            throw Spark.halt(HttpStatus.FORBIDDEN_403);
        }

        // Get month and year parameters
        String monthParam = request.queryParams("month");
        String yearParam = request.queryParams("year");

        Integer month = null;
        Integer year = null;

        try {
            if (monthParam != null) {
                month = Integer.valueOf(monthParam);
            }
            if (yearParam != null) {
                year = Integer.valueOf(yearParam);
            }
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid month/year format", e);
        }

        // Default to current month/year if parameters are missing or invalid
        if (month == null || year == null) {
            Date now = new Date();
            month = TimeUtils.month(now);
            year = TimeUtils.year(now);
        }

        // Default to current month if not specified
        if (month == null || year == null) {
            Date now = new Date();
            month = TimeUtils.month(now);
            year = TimeUtils.year(now);
        }

        // Calculate date range for the specified month
        Date startDate = TimeUtils.toDate("01", month.toString(), year.toString());
        startDate = TimeUtils.beginOfMonth(startDate);
        Date endDate = TimeUtils.toDate("01", month.toString(), year.toString());
        endDate = TimeUtils.endOfMonth(endDate);

        try {
            // Get statistics from DAO
            List<Map<String, Object>> statistics = ProcedureDao.loadProcedureStatisticsByUser(startDate, endDate);

            // Get all users for reference
            List<User> allUsers = UserDao.loadUserList();
            Map<ObjectId, String> userNames = new HashMap<>();
            for (User u : allUsers) {
                String fullName = (StringUtils.isNotBlank(u.getName()) ? u.getName() : "") +
                                 (StringUtils.isNotBlank(u.getLastname()) ? " " + u.getLastname() : "");
                if (StringUtils.isBlank(fullName.trim())) {
                    fullName = u.getFullname();
                }
                userNames.put(u.getId(), StringUtils.defaultIfBlank(fullName.trim(), "Utente sconosciuto"));
            }

            // Format response
            Map<String, Object> result = new HashMap<>();
            result.put("month", month);
            result.put("year", year);
            result.put("statistics", statistics);
            result.put("userNames", userNames);

            return result;

        } catch (Exception ex) {
            LOGGER.error("Error loading procedure statistics", ex);
            throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500);
        }
    };

    ////////////
    // internals
    private static String imageAsDatauri(ObjectId imageId) {
        String datauri = null;
        
        if (imageId != null) {

            Image img = null;
            try {
                img = ImageDao.loadImage(imageId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (img != null) {
                
                // create "data URI"
                datauri = ImageUtils.toDatauri(img.getContentType(), img.getBytes());
                
            } else {
                LOGGER.warn("empty image oid " + imageId);
            }
        } else {
            LOGGER.warn("unexistent image oid " + imageId);
        }
        
        return datauri;
    }

    private static String encode(String value) {
        if (StringUtils.isNotBlank(value)) {
            try {
                String encoded = URLEncoder.encode(value, "UTF-8");
                value = encoded;
            } catch (Exception ex) {
                LOGGER.error("unable to encode value", ex);
            }
        }
        return value;
    }
    
}
