package com.miocontotermico.procedurenote;

import com.google.gson.Gson;
import com.miocontotermico.core.Manager;
import com.miocontotermico.dao.ProcedureNoteDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.ProcedureNote;
import com.miocontotermico.pojo.User;
import com.miocontotermico.util.ParamUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for procedure notes operations
 * 
 * <AUTHOR> Augster
 */
public class ProcedureNoteController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ProcedureNoteController.class.getName());
    private static final Gson gson = new Gson();
    
    public static Route loadProcedureNotes = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null || !user.getProfileType().equals("admin")) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        if (procedureId == null) {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
            return null;
        }
        
        try {
            List<ProcedureNote> notes = ProcedureNoteDao.loadProcedureNotesByProcedureId(procedureId);

            // Create enhanced notes with user names
            List<Map<String, Object>> enhancedNotes = new ArrayList<>();
            for (ProcedureNote note : notes) {
                Map<String, Object> noteMap = new HashMap<>();
                noteMap.put("id", note.getId().toString());
                noteMap.put("procedureId", note.getProcedureId());
                noteMap.put("type", note.getType());
                noteMap.put("content", note.getContent());
                noteMap.put("userId", note.getUserId());
                noteMap.put("creation", note.getCreation());
                noteMap.put("lastUpdate", note.getLastUpdate());

                // Add user name if available
                if (note.getUserId() != null) {
                    try {
                        User noteUser = UserDao.loadUser(note.getUserId());
                        if (noteUser != null) {
                            noteMap.put("userName", noteUser.getName() + " " + noteUser.getLastname());
                        }
                    } catch (Exception ex) {
                        LOGGER.warn("Could not load user for note", ex);
                    }
                }

                enhancedNotes.add(noteMap);
            }

            response.type("application/json");
            return gson.toJson(enhancedNotes);
            
        } catch (Exception ex) {
            LOGGER.error("Error loading procedure notes", ex);
            Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return null;
        }
    };
    
    public static Route createProcedureNote = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null || !user.getProfileType().equals("admin")) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        String type = request.queryParams("type");
        String content = request.queryParams("content");
        
        if (procedureId == null || StringUtils.isBlank(type) || StringUtils.isBlank(content)) {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
            return null;
        }
        
        // Validate type
        if (!type.equals("semplice") && !type.equals("attenzione") && !type.equals("problema")) {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
            return null;
        }
        
        try {
            ProcedureNote note = new ProcedureNote();
            note.setProcedureId(procedureId);
            note.setType(type);
            note.setContent(content);
            note.setUserId(user.getId());
            
            ObjectId noteId = ProcedureNoteDao.insertProcedureNote(note);
            note.setId(noteId);
            
            response.type("application/json");
            return gson.toJson(note);
            
        } catch (Exception ex) {
            LOGGER.error("Error creating procedure note", ex);
            Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return null;
        }
    };
    
    public static Route updateProcedureNote = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null || !user.getProfileType().equals("admin")) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        ObjectId noteId = ParamUtils.toObjectId(request.queryParams("noteId"));
        String type = request.queryParams("type");
        String content = request.queryParams("content");
        
        if (noteId == null || StringUtils.isBlank(type) || StringUtils.isBlank(content)) {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
            return null;
        }
        
        // Validate type
        if (!type.equals("semplice") && !type.equals("attenzione") && !type.equals("problema")) {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
            return null;
        }
        
        try {
            ProcedureNote note = ProcedureNoteDao.loadProcedureNote(noteId);
            if (note == null) {
                Spark.halt(HttpStatus.NOT_FOUND_404);
                return null;
            }
            
            note.setType(type);
            note.setContent(content);
            
            ProcedureNoteDao.updateProcedureNote(note);
            
            response.type("application/json");
            return gson.toJson(note);
            
        } catch (Exception ex) {
            LOGGER.error("Error updating procedure note", ex);
            Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return null;
        }
    };
    
    public static Route deleteProcedureNote = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null || !user.getProfileType().equals("admin")) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        ObjectId noteId = ParamUtils.toObjectId(request.queryParams("noteId"));
        if (noteId == null) {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
            return null;
        }
        
        try {
            ProcedureNoteDao.deleteProcedureNote(noteId);
            return "ok";
            
        } catch (Exception ex) {
            LOGGER.error("Error deleting procedure note", ex);
            Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500);
            return null;
        }
    };
}
