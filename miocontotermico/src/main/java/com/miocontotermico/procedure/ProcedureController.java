package com.miocontotermico.procedure;

import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.commons.ProcedureCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.CounterDao;
import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.FileType;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.pojo.types.ServiceType;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.PropertyUtils;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.support.file.posted.PostedFile;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.util.TimeUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.*;

import java.io.File;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class ProcedureController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcedureController.class.getName());

    public static TemplateViewRoute procedures = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<StatusEntry> statusList = ProcedureDao.loadProcedureStatusList();
        attributes.put("statusList", PropertyUtils.cleanStatusList(statusList));
        
        
        // order (optional) filters
        Date startDate = DateUtils.addMonths(new Date(), -3);
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        String[] selectedStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedStatuses"))) {
            selectedStatuses = StringUtils.split(request.queryParams("selectedStatuses"), "|");
        }
        attributes.put("selectedStatuses", selectedStatuses);
        
        ObjectId userId = null;
        ObjectId assignedUserId = null;
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
                assignedUserId = user.getId();
            } else {
                userId = user.getId();
            }
        }
//        if (user.getProfileType() !=)
        List<Procedure> procedureList = ProcedureDao.loadProcedureListByDateRangeAndStatus(startDate, endDate, selectedStatuses, userId, assignedUserId);
        attributes.put("procedureList", ProcedureCommons.toEntries(procedureList));
        
        return Manager.render(Templates.PROCEDURES, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute procedures_add = (Request request, Response response) -> {
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        if (user.getCredit() == null || user.getCredit() < 80) {
            response.redirect(RouteUtils.contextPath(request) + Paths.BUY_CREDIT + "?insufficient=true") ;
        }

        // draft
        Procedure draft = ProcedureDao.loadDraftProcedure(user.getId());
        if (draft == null) {
            draft = ProcedureCommons.initDraft(user);
//        } else {
//            if (!user.getProfileType().equals("privato")) {
//                draft = ProcedureCommons.initExistingDraft(draft);
//            }
        }
        attributes.put("procedure", draft);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // step
        attributes.put("step", NumberUtils.toInt(request.queryParams("step"), 0));
        
        attributes.put("tab", request.queryParams("tab"));
        
        return Manager.render(Templates.PROCEDURES_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route procedures_add_info_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Procedure draft = ProcedureDao.loadDraftProcedure(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        PojoUtils.mergeFromParams(params, draft);
        
        // files
        if (!files.isEmpty()) {
            
            String fieldname = "thermalPortalDelegationFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getThermalPortalDelegationFileIds() == null) {
                    draft.setThermalPortalDelegationFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getThermalPortalDelegationFileIds().addAll(ids);
                }
            }
            
            fieldname = "identityDocumentFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getIdentityDocumentFileIds() == null) {
                    draft.setIdentityDocumentFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getIdentityDocumentFileIds().addAll(ids);
                }
            }
            
            fieldname = "tinFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getTinFileIds() == null) {
                    draft.setTinFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getTinFileIds().addAll(ids);
                }
            }
            
            fieldname = "authorizationFormFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getAuthorizationFormFileIds() == null) {
                    draft.setAuthorizationFormFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getAuthorizationFormFileIds().addAll(ids);
                }
            }
            
            fieldname = "identityDocumentOwnerFormFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getIdentityDocumentOwnerFormFileIds() == null) {
                    draft.setIdentityDocumentOwnerFormFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getIdentityDocumentOwnerFormFileIds().addAll(ids);
                }
            }
            
            fieldname = "invoiceFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getInvoiceFileIds() == null) {
                    draft.setInvoiceFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getInvoiceFileIds().addAll(ids);
                }
            }
            
            fieldname = "contractFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getContractFileIds() == null) {
                    draft.setContractFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getContractFileIds().addAll(ids);
                }
            }
            
            fieldname = "dataCollectionFormFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDataCollectionFormFileIds() == null) {
                    draft.setDataCollectionFormFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDataCollectionFormFileIds().addAll(ids);
                }
            }
            
            fieldname = "technicalSheetFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getTechnicalSheetFileIds() == null) {
                    draft.setTechnicalSheetFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getTechnicalSheetFileIds().addAll(ids);
                }
            }
            
            fieldname = "disposalDocumentFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDisposalDocumentFileIds() == null) {
                    draft.setDisposalDocumentFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDisposalDocumentFileIds().addAll(ids);
                }
            }
            
            fieldname = "identityDocumentAgentFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getIdentityDocumentAgentFileIds() == null) {
                    draft.setIdentityDocumentAgentFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getIdentityDocumentAgentFileIds().addAll(ids);
                }
            }
            
            fieldname = "cashingMandateFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getCashingMandateFileIds() == null) {
                    draft.setCashingMandateFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getCashingMandateFileIds().addAll(ids);
                }
            }

            fieldname = "plate1FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getPlate1FileIds() == null) {
                    draft.setPlate1FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getPlate1FileIds().addAll(ids);
                }
            }

            fieldname = "thermalPlant1FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getThermalPlant1FileIds() == null) {
                    draft.setThermalPlant1FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getThermalPlant1FileIds().addAll(ids);
                }
            }

            fieldname = "generator1FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGenerator1FileIds() == null) {
                    draft.setGenerator1FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGenerator1FileIds().addAll(ids);
                }
            }

            fieldname = "valves1FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getValves1FileIds() == null) {
                    draft.setValves1FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getValves1FileIds().addAll(ids);
                }
            }

            fieldname = "plate2FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getPlate2FileIds() == null) {
                    draft.setPlate2FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getPlate2FileIds().addAll(ids);
                }
            }

            fieldname = "thermal2PlantFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getThermal2PlantFileIds() == null) {
                    draft.setThermal2PlantFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getThermal2PlantFileIds().addAll(ids);
                }
            }

            fieldname = "generator2FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGenerator2FileIds() == null) {
                    draft.setGenerator2FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGenerator2FileIds().addAll(ids);
                }
            }

            fieldname = "valves2FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getValves2FileIds() == null) {
                    draft.setValves2FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getValves2FileIds().addAll(ids);
                }
            }

            fieldname = "globalStorage2FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGlobalStorage2FileIds() == null) {
                    draft.setGlobalStorage2FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGlobalStorage2FileIds().addAll(ids);
                }
            }
            
            fieldname = "detailPanel3FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDetailPanel3FileIds() == null) {
                    draft.setDetailPanel3FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDetailPanel3FileIds().addAll(ids);
                }
            }
            
            fieldname = "detailPlate3FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDetailPlate3FileIds() == null) {
                    draft.setDetailPlate3FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDetailPlate3FileIds().addAll(ids);
                }
            }
            
            fieldname = "detailBoiler3FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDetailBoiler3FileIds() == null) {
                    draft.setDetailBoiler3FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDetailBoiler3FileIds().addAll(ids);
                }
            }
            
            fieldname = "globalInstalling3FieldFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGlobalInstalling3FieldFileIds() == null) {
                    draft.setGlobalInstalling3FieldFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGlobalInstalling3FieldFileIds().addAll(ids);
                }
            }
            
            fieldname = "globalField3FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGlobalField3FileIds() == null) {
                    draft.setGlobalField3FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGlobalField3FileIds().addAll(ids);
                }
            }
            
            fieldname = "valves3FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getValves3FileIds() == null) {
                    draft.setValves3FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getValves3FileIds().addAll(ids);
                }
            }
            
            fieldname = "detailGenerator4FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDetailGenerator4FileIds() == null) {
                    draft.setDetailGenerator4FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDetailGenerator4FileIds().addAll(ids);
                }
            }
            
            fieldname = "globalGenerator4FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGlobalGenerator4FileIds() == null) {
                    draft.setGlobalGenerator4FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGlobalGenerator4FileIds().addAll(ids);
                }
            }
            
            fieldname = "plate4FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getPlate4FileIds() == null) {
                    draft.setPlate4FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getPlate4FileIds().addAll(ids);
                }
            }

            fieldname = "plate5FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getPlate5FileIds() == null) {
                    draft.setPlate5FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getPlate5FileIds().addAll(ids);
                }
            }

            fieldname = "generator5FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getGenerator5FileIds() == null) {
                    draft.setGenerator5FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getGenerator5FileIds().addAll(ids);
                }
            }

            fieldname = "thermalPlant5FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getThermalPlant5FileIds() == null) {
                    draft.setThermalPlant5FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getThermalPlant5FileIds().addAll(ids);
                }
            }

            fieldname = "valves5FileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getValves5FileIds() == null) {
                    draft.setValves5FileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getValves5FileIds().addAll(ids);
                }
            }
            
        }
        
        // save
        try {
            ProcedureDao.updateProcedure(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route procedures_add_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Procedure draft = ProcedureDao.loadDraftProcedure(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        PojoUtils.mergeFromParams(params, draft);

        if (StringUtils.isBlank(draft.getService())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Servizio non selezionato");
        }
        
        int creditToSave = ServiceType.valueOf(draft.getService()).getCredit();
        
        if (draft.getPriority() != null && draft.getPriority() == true) {
            creditToSave = creditToSave + 30;
        }
        
        if (creditToSave > user.getCredit()) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Crediti non sufficienti");
        }
        
        // data
        draft.setStatus(StatusType.opened.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setDate(TimeUtils.now());

        // protocol
        String protocolKey = "procedure-protocol";
        int next = CounterDao.next(protocolKey);
        draft.setProtocol("" + next);
        
        // save
        try {
            ProcedureDao.updateProcedure(draft);
            
            Integer credit = user.getCredit() != null ? user.getCredit() : 0;
            credit -= creditToSave;
            user.setCredit(credit);
            UserDao.updateUser(user);
            Manager.putSession(token, "user", user);

            // INVIARE MAIL A SOGENIT
            if (NotificationCommons.notifyProcedureReceive(request, draft)) {
                // ...
            }
            if (draft.getUserId() != null) {
                User procedureUser = UserDao.loadUser(draft.getUserId());
                if (NotificationCommons.notifyProcedureReceiveToUser(request, draft, procedureUser)) {
                    // ...
                }
            }
            
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route procedures_add_fileid_remove = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Procedure draft = ProcedureDao.loadDraftProcedure(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // id
        ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
        if (fileId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        List<ObjectId> ids = null;
        switch (listName) {
            case "thermalPortalDelegationFileIds":
                ids = draft.getThermalPortalDelegationFileIds();
                break;
            case "identityDocumentFileIds":
                ids = draft.getIdentityDocumentFileIds();
                break;
            case "tinFileIds":
                ids = draft.getTinFileIds();
                break;
            case "authorizationFormFileIds":
                ids = draft.getAuthorizationFormFileIds();
                break;
            case "identityDocumentOwnerFormFileIds":
                ids = draft.getIdentityDocumentOwnerFormFileIds();
                break;
            case "invoiceFileIds":
                ids = draft.getInvoiceFileIds();
                break;
            case "contractFileIds":
                ids = draft.getContractFileIds();
                break;
            case "dataCollectionFormFileIds":
                ids = draft.getDataCollectionFormFileIds();
                break;
            case "technicalSheetFileIds":
                ids = draft.getTechnicalSheetFileIds();
                break;
            case "disposalDocumentFileIds":
                ids = draft.getDisposalDocumentFileIds();
                break;
            case "identityDocumentAgentFileIds":
                ids = draft.getIdentityDocumentAgentFileIds();
                break;
            case "cashingMandateFileIds":
                ids = draft.getCashingMandateFileIds();
                break;
            case "plate1FileIds":
                ids = draft.getPlate1FileIds();
                break;
            case "thermalPlant1FileIds":
                ids = draft.getThermalPlant1FileIds();
                break;
            case "generator1FileIds":
                ids = draft.getGenerator1FileIds();
                break;
            case "valves1FileIds":
                ids = draft.getValves1FileIds();
                break;
            case "plate2FileIds":
                ids = draft.getPlate2FileIds();
                break;
            case "thermal2PlantFileIds":
                ids = draft.getThermal2PlantFileIds();
                break;
            case "generator2FileIds":
                ids = draft.getGenerator2FileIds();
                break;
            case "valves2FileIds":
                ids = draft.getValves2FileIds();
                break;
            case "globalStorage2FileIds":
                ids = draft.getGlobalStorage2FileIds();
                break;
            case "detailPanel3FileIds":
                ids = draft.getDetailPanel3FileIds();
                break;
            case "detailPlate3FileIds":
                ids = draft.getDetailPlate3FileIds();
                break;
            case "detailBoiler3FileIds":
                ids = draft.getDetailBoiler3FileIds();
                break;
            case "globalInstalling3FieldFileIds":
                ids = draft.getGlobalInstalling3FieldFileIds();
                break;
            case "globalField3FileIds":
                ids = draft.getGlobalField3FileIds();
                break;
            case "valves3FileIds":
                ids = draft.getValves3FileIds();
                break;
            case "detailGenerator4FileIds":
                ids = draft.getDetailGenerator4FileIds();
                break;
            case "globalGenerator4FileIds":
                ids = draft.getGlobalGenerator4FileIds();
                break;
            case "plate4FileIds":
                ids = draft.getPlate4FileIds();
                break;
            case "plate5FileIds":
                ids = draft.getPlate5FileIds();
                break;
            case "generator5FileIds":
                ids = draft.getGenerator5FileIds();
                break;
            case "thermalPlant5FileIds":
                ids = draft.getPlate5FileIds();
                break;
            case "valves5FileIds":
                ids = draft.getValves5FileIds();
                break;
            default:
                break;
        }
        if (ids == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // remove id
        Iterator<ObjectId> iter = ids.iterator();
        while (iter.hasNext()) {
            if (iter.next().equals(fileId)) {
                iter.remove();
                break;
            }
        }
        
        // manca rimozione lista
        // ??????
        
        // save
        try {
            ProcedureDao.updateProcedure(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static TemplateViewRoute procedure_edit = (Request request, Response response) -> {
        
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        Procedure procedure = null;
        if (procedureId != null) {
            procedure = ProcedureDao.loadProcedure(procedureId);
        }
        attributes.put("procedure", procedure);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<User> userList = UserDao.loadSystemUserList();
        attributes.put("userList", userList);

        return Manager.render(Templates.PROCEDURE_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route procedure_edit_save = (Request request, Response response) -> {
        // params
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        
        if (procedureId != null) {
            // params
            Procedure procedure = ProcedureDao.loadProcedure(procedureId);
            if (procedure != null) {
                Boolean sendDocumentMail = false;
                Boolean sendFinalMail = false;
                Boolean sendAssignedMail = false;
                Boolean sendUpdateStatusMail = false;
                
                if (StringUtils.isNotBlank(params.get("status"))) {
                    if (!StringUtils.equalsIgnoreCase(procedure.getStatus(), params.get("status"))) {
                        sendUpdateStatusMail = true;
                    }
                    procedure.setStatus(params.get("status"));
                }
                if (ParamUtils.toObjectId(params.get("assignedUserId")) != null) {
                    if (procedure.getAssignedUserId() != null) {
                        if (!procedure.getAssignedUserId().equals(ParamUtils.toObjectId(params.get("assignedUserId")))) {
                            sendAssignedMail = true;
                        }
                    } else {
                        sendAssignedMail = true;
                    }
                    procedure.setAssignedUserId(ParamUtils.toObjectId(params.get("assignedUserId")));
                }

                // Handle all personal information fields
                if (StringUtils.isNotBlank(params.get("profileType"))) {
                    procedure.setProfileType(params.get("profileType"));
                }
                if (StringUtils.isNotBlank(params.get("name"))) {
                    procedure.setName(params.get("name"));
                }
                if (StringUtils.isNotBlank(params.get("lastname"))) {
                    procedure.setLastname(params.get("lastname"));
                }
                if (StringUtils.isNotBlank(params.get("fullname"))) {
                    procedure.setFullname(params.get("fullname"));
                }
                if (StringUtils.isNotBlank(params.get("tin"))) {
                    procedure.setTin(params.get("tin"));
                }
                if (StringUtils.isNotBlank(params.get("vatNumber"))) {
                    procedure.setVatNumber(params.get("vatNumber"));
                }
                if (StringUtils.isNotBlank(params.get("email"))) {
                    procedure.setEmail(params.get("email"));
                }
                if (StringUtils.isNotBlank(params.get("pec"))) {
                    procedure.setPec(params.get("pec"));
                }
                if (StringUtils.isNotBlank(params.get("phoneNumber"))) {
                    procedure.setPhoneNumber(params.get("phoneNumber"));
                }

                // Handle address information fields
                if (StringUtils.isNotBlank(params.get("address"))) {
                    procedure.setAddress(params.get("address"));
                }
                if (StringUtils.isNotBlank(params.get("city"))) {
                    procedure.setCity(params.get("city"));
                }
                if (StringUtils.isNotBlank(params.get("provinceCode"))) {
                    procedure.setProvinceCode(params.get("provinceCode"));
                }
                if (StringUtils.isNotBlank(params.get("postalCode"))) {
                    procedure.setPostalCode(params.get("postalCode"));
                }
                if (StringUtils.isNotBlank(params.get("countryCode"))) {
                    procedure.setCountryCode(params.get("countryCode"));
                }

                // Handle financial information fields
                if (StringUtils.isNotBlank(params.get("iban"))) {
                    procedure.setIban(params.get("iban"));
                }
                if (StringUtils.isNotBlank(params.get("coupon"))) {
                    procedure.setCoupon(params.get("coupon"));
                }

                // Handle ownership field
                if (StringUtils.isNotBlank(params.get("ownership"))) {
                    procedure.setOwnership(params.get("ownership"));
                }

                // Handle additional fields
                if (StringUtils.isNotBlank(params.get("pin"))) {
                    procedure.setPin(params.get("pin"));
                }
                if (StringUtils.isNotBlank(params.get("lastPaymentDate"))) {
                    try {
                        Date lastPaymentDate = DateUtils.parseDate(params.get("lastPaymentDate"), "dd/MM/yyyy");
                        procedure.setLastPaymentDate(lastPaymentDate);
                    } catch (Exception ex) {
                        LOGGER.error("Error parsing lastPaymentDate", ex);
                    }
                }

                if (!files.isEmpty()) {
                    String fieldname = "finalConventionFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getFinalConventionFileIds()== null) {
                            procedure.setFinalConventionFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getFinalConventionFileIds().addAll(ids);
                            sendFinalMail = true;
                        }
                    }

                    fieldname = "finalConventionToSignedIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getFinalConventionToSignedIds()== null) {
                            procedure.setFinalConventionToSignedIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getFinalConventionToSignedIds().addAll(ids);
                            sendDocumentMail = true;
                        }
                    }

                    fieldname = "internalFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getInternalFileIds()== null) {
                            procedure.setInternalFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getInternalFileIds().addAll(ids);
                        }
                    }

                    // Handle new document uploads
                    fieldname = "thermalPortalDelegationFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getThermalPortalDelegationFileIds() == null) {
                            procedure.setThermalPortalDelegationFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getThermalPortalDelegationFileIds().addAll(ids);
                        }
                    }

                    fieldname = "identityDocumentFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getIdentityDocumentFileIds() == null) {
                            procedure.setIdentityDocumentFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getIdentityDocumentFileIds().addAll(ids);
                        }
                    }

                    fieldname = "tinFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getTinFileIds() == null) {
                            procedure.setTinFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getTinFileIds().addAll(ids);
                        }
                    }

                    fieldname = "authorizationFormFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getAuthorizationFormFileIds() == null) {
                            procedure.setAuthorizationFormFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getAuthorizationFormFileIds().addAll(ids);
                        }
                    }

                    fieldname = "identityDocumentOwnerFormFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getIdentityDocumentOwnerFormFileIds() == null) {
                            procedure.setIdentityDocumentOwnerFormFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getIdentityDocumentOwnerFormFileIds().addAll(ids);
                        }
                    }

                    fieldname = "invoiceFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getInvoiceFileIds() == null) {
                            procedure.setInvoiceFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getInvoiceFileIds().addAll(ids);
                        }
                    }

                    fieldname = "contractFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getContractFileIds() == null) {
                            procedure.setContractFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getContractFileIds().addAll(ids);
                        }
                    }

                    fieldname = "dataCollectionFormFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getDataCollectionFormFileIds() == null) {
                            procedure.setDataCollectionFormFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getDataCollectionFormFileIds().addAll(ids);
                        }
                    }

                    fieldname = "technicalSheetFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getTechnicalSheetFileIds() == null) {
                            procedure.setTechnicalSheetFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getTechnicalSheetFileIds().addAll(ids);
                        }
                    }

                    fieldname = "disposalDocumentFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getDisposalDocumentFileIds() == null) {
                            procedure.setDisposalDocumentFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getDisposalDocumentFileIds().addAll(ids);
                        }
                    }

                    fieldname = "identityDocumentAgentFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getIdentityDocumentAgentFileIds() == null) {
                            procedure.setIdentityDocumentAgentFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getIdentityDocumentAgentFileIds().addAll(ids);
                        }
                    }

                    fieldname = "cashingMandateFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getCashingMandateFileIds() == null) {
                            procedure.setCashingMandateFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getCashingMandateFileIds().addAll(ids);
                        }
                    }

                    // Technical documents
                    fieldname = "plate1FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getPlate1FileIds() == null) {
                            procedure.setPlate1FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getPlate1FileIds().addAll(ids);
                        }
                    }

                    fieldname = "thermalPlant1FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getThermalPlant1FileIds() == null) {
                            procedure.setThermalPlant1FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getThermalPlant1FileIds().addAll(ids);
                        }
                    }

                    fieldname = "generator1FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGenerator1FileIds() == null) {
                            procedure.setGenerator1FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGenerator1FileIds().addAll(ids);
                        }
                    }

                    fieldname = "valves1FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getValves1FileIds() == null) {
                            procedure.setValves1FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getValves1FileIds().addAll(ids);
                        }
                    }

                    // Additional technical documents
                    fieldname = "plate2FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getPlate2FileIds() == null) {
                            procedure.setPlate2FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getPlate2FileIds().addAll(ids);
                        }
                    }

                    fieldname = "thermal2PlantFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getThermal2PlantFileIds() == null) {
                            procedure.setThermal2PlantFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getThermal2PlantFileIds().addAll(ids);
                        }
                    }

                    fieldname = "generator2FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGenerator2FileIds() == null) {
                            procedure.setGenerator2FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGenerator2FileIds().addAll(ids);
                        }
                    }

                    fieldname = "valves2FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getValves2FileIds() == null) {
                            procedure.setValves2FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getValves2FileIds().addAll(ids);
                        }
                    }

                    fieldname = "globalStorage2FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGlobalStorage2FileIds() == null) {
                            procedure.setGlobalStorage2FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGlobalStorage2FileIds().addAll(ids);
                        }
                    }

                    fieldname = "plate5FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getPlate5FileIds() == null) {
                            procedure.setPlate5FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getPlate5FileIds().addAll(ids);
                        }
                    }

                    fieldname = "generator5FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGenerator5FileIds() == null) {
                            procedure.setGenerator5FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGenerator5FileIds().addAll(ids);
                        }
                    }

                    fieldname = "thermalPlant5FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getThermalPlant5FileIds() == null) {
                            procedure.setThermalPlant5FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getThermalPlant5FileIds().addAll(ids);
                        }
                    }

                    fieldname = "valves5FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getValves5FileIds() == null) {
                            procedure.setValves5FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getValves5FileIds().addAll(ids);
                        }
                    }

                    // Additional technical documents - Group 3
                    fieldname = "detailPanel3FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getDetailPanel3FileIds() == null) {
                            procedure.setDetailPanel3FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getDetailPanel3FileIds().addAll(ids);
                        }
                    }

                    fieldname = "detailPlate3FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getDetailPlate3FileIds() == null) {
                            procedure.setDetailPlate3FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getDetailPlate3FileIds().addAll(ids);
                        }
                    }

                    fieldname = "detailBoiler3FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getDetailBoiler3FileIds() == null) {
                            procedure.setDetailBoiler3FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getDetailBoiler3FileIds().addAll(ids);
                        }
                    }

                    fieldname = "globalInstalling3FieldFileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGlobalInstalling3FieldFileIds() == null) {
                            procedure.setGlobalInstalling3FieldFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGlobalInstalling3FieldFileIds().addAll(ids);
                        }
                    }

                    fieldname = "globalField3FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGlobalField3FileIds() == null) {
                            procedure.setGlobalField3FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGlobalField3FileIds().addAll(ids);
                        }
                    }

                    fieldname = "valves3FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getValves3FileIds() == null) {
                            procedure.setValves3FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getValves3FileIds().addAll(ids);
                        }
                    }

                    // Additional technical documents - Group 4
                    fieldname = "detailGenerator4FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getDetailGenerator4FileIds() == null) {
                            procedure.setDetailGenerator4FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getDetailGenerator4FileIds().addAll(ids);
                        }
                    }

                    fieldname = "globalGenerator4FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getGlobalGenerator4FileIds() == null) {
                            procedure.setGlobalGenerator4FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getGlobalGenerator4FileIds().addAll(ids);
                        }
                    }

                    fieldname = "plate4FileIds";
                    if (files.containsKey(fieldname)) {
                        if (procedure.getPlate4FileIds() == null) {
                            procedure.setPlate4FileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            procedure.getPlate4FileIds().addAll(ids);
                        }
                    }
                }

                // save
                try {
                    ProcedureDao.updateProcedure(procedure);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                // notify
                if (sendFinalMail) {
                    if (NotificationCommons.notifyProcedureFinalUpload(request, procedure)) {
                        // ...
                    }
                }
                if (sendDocumentMail) {
                    if (NotificationCommons.notifyProcedureUpload(request, procedure)) {
                        // ...
                    }
                }
                if (sendAssignedMail) {
                    if (procedure.getUserId() != null) {
                        User userAssigned = UserDao.loadUser(procedure.getAssignedUserId());
                        if (NotificationCommons.notifyProcedureAssigned(request, procedure, userAssigned)) {
                            // ...
                        }
                    }
                }
                if (sendUpdateStatusMail) {
                    if (procedure.getUserId() != null) {
                        User procedureUser = UserDao.loadUser(procedure.getUserId());
                        if (NotificationCommons.notifyProcedure(request, procedure, procedureUser)) {
                            // ...
                        }
                    }
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        // files
        
        

        return "ok";        

    };  
    
    public static Route procedures_edit_fileid_remove = (Request request, Response response) -> {
        
        // params
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        if (procedureId != null) {
            Procedure procedure = ProcedureDao.loadProcedure(procedureId);
            if (procedure != null) {
                // id
                ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
                if (fileId == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

                List<ObjectId> ids = null;
                switch (listName) {
                    case "finalConventionFileIds":
                        ids = procedure.getFinalConventionFileIds();
                        break;
                    case "finalConventionToSignedIds":
                        ids = procedure.getFinalConventionToSignedIds();
                        break;
                    case "internalFileIds":
                        ids = procedure.getInternalFileIds();
                        break;
                    case "thermalPortalDelegationFileIds":
                        ids = procedure.getThermalPortalDelegationFileIds();
                        break;
                    case "identityDocumentFileIds":
                        ids = procedure.getIdentityDocumentFileIds();
                        break;
                    case "tinFileIds":
                        ids = procedure.getTinFileIds();
                        break;
                    case "authorizationFormFileIds":
                        ids = procedure.getAuthorizationFormFileIds();
                        break;
                    case "identityDocumentOwnerFormFileIds":
                        ids = procedure.getIdentityDocumentOwnerFormFileIds();
                        break;
                    case "invoiceFileIds":
                        ids = procedure.getInvoiceFileIds();
                        break;
                    case "contractFileIds":
                        ids = procedure.getContractFileIds();
                        break;
                    case "dataCollectionFormFileIds":
                        ids = procedure.getDataCollectionFormFileIds();
                        break;
                    case "technicalSheetFileIds":
                        ids = procedure.getTechnicalSheetFileIds();
                        break;
                    case "disposalDocumentFileIds":
                        ids = procedure.getDisposalDocumentFileIds();
                        break;
                    case "identityDocumentAgentFileIds":
                        ids = procedure.getIdentityDocumentAgentFileIds();
                        break;
                    case "cashingMandateFileIds":
                        ids = procedure.getCashingMandateFileIds();
                        break;
                    case "plate1FileIds":
                        ids = procedure.getPlate1FileIds();
                        break;
                    case "thermalPlant1FileIds":
                        ids = procedure.getThermalPlant1FileIds();
                        break;
                    case "generator1FileIds":
                        ids = procedure.getGenerator1FileIds();
                        break;
                    case "valves1FileIds":
                        ids = procedure.getValves1FileIds();
                        break;
                    case "plate2FileIds":
                        ids = procedure.getPlate2FileIds();
                        break;
                    case "thermal2PlantFileIds":
                        ids = procedure.getThermal2PlantFileIds();
                        break;
                    case "generator2FileIds":
                        ids = procedure.getGenerator2FileIds();
                        break;
                    case "valves2FileIds":
                        ids = procedure.getValves2FileIds();
                        break;
                    case "globalStorage2FileIds":
                        ids = procedure.getGlobalStorage2FileIds();
                        break;
                    case "detailPanel3FileIds":
                        ids = procedure.getDetailPanel3FileIds();
                        break;
                    case "detailPlate3FileIds":
                        ids = procedure.getDetailPlate3FileIds();
                        break;
                    case "detailBoiler3FileIds":
                        ids = procedure.getDetailBoiler3FileIds();
                        break;
                    case "globalInstalling3FieldFileIds":
                        ids = procedure.getGlobalInstalling3FieldFileIds();
                        break;
                    case "globalField3FileIds":
                        ids = procedure.getGlobalField3FileIds();
                        break;
                    case "valves3FileIds":
                        ids = procedure.getValves3FileIds();
                        break;
                    case "detailGenerator4FileIds":
                        ids = procedure.getDetailGenerator4FileIds();
                        break;
                    case "globalGenerator4FileIds":
                        ids = procedure.getGlobalGenerator4FileIds();
                        break;
                    case "plate4FileIds":
                        ids = procedure.getPlate4FileIds();
                        break;
                    case "plate5FileIds":
                        ids = procedure.getPlate5FileIds();
                        break;
                    case "generator5FileIds":
                        ids = procedure.getGenerator5FileIds();
                        break;
                    case "thermalPlant5FileIds":
                        ids = procedure.getThermalPlant5FileIds();
                        break;
                    case "valves5FileIds":
                        ids = procedure.getValves5FileIds();
                        break;
                    default:
                        break;
                }
                if (ids == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

                // remove id
                Iterator<ObjectId> iter = ids.iterator();
                while (iter.hasNext()) {
                    if (iter.next().equals(fileId)) {
                        iter.remove();
                        break;
                    }
                }

                // manca rimozione lista
                // ??????

                // save
                try {
                    ProcedureDao.updateProcedure(procedure);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };
    
    public static Route procedure_remove = (Request request, Response response) -> {
        
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (procedureId != null) {
            // params
            Procedure procedure = ProcedureDao.loadProcedure(procedureId);
            if (procedure != null) {
                ProcedureDao.updateProcedureCancelled(procedureId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };          
    
    public static Route procedure_status_update = (Request request, Response response) -> {
        
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        String status = request.queryParams("status");
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (procedureId != null) {
            // params
            Procedure procedure = ProcedureDao.loadProcedure(procedureId);
            if (procedure != null) {
                procedure.setStatus(status);
                ProcedureDao.updateProcedure(procedure);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
            // notify
            if (procedure.getUserId() != null) {
                User procedureUser = UserDao.loadUser(procedure.getUserId());
                if (NotificationCommons.notifyProcedure(request, procedure, procedureUser)) {
                    // ...
                }
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };



    ////////////
    // internals

    private static List<ObjectId> insertFiles(List<PostedFile> posteds) {
        if (posteds == null) {
            return null;
        }
        if (posteds.isEmpty()) {
            return null;
        }
        List<ObjectId> ids = new ArrayList<>();
        for (PostedFile posted : posteds) {
            
            ObjectId oid = null;
            try {
                
                // filename
                String filename = FileDao.composeFilename(FileType.attachment, posted.getExtension());
                
                // save file
                File fll = new File(posted.getFilename());
                oid = FileDao.insertFile(filename, 
                        posted.getName(),
                        posted.getContentType(), 
                        FileUtils.readFileToByteArray(fll)
                );
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (oid != null) {
                ids.add(oid);
            }
        }
        return ids;
    }
    
}
