package com.miocontotermico.statistics;

import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.util.RouteUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

import java.util.HashMap;
import java.util.Map;

/**
 * Statistics Controller
 * Handles statistics pages for admin and operator users
 * 
 * <AUTHOR>
 */
public class StatisticsController {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatisticsController.class.getName());

    /**
     * Statistics main page
     * Shows procedure statistics dashboard for admin and operator users
     */
    public static TemplateViewRoute statistics = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }

        // Check if user is admin or operator
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
            !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.STATISTICS, attributes, RouteUtils.pathType(request));
    };
}
