<!DOCTYPE html>
<html lang="it">

    <head>

        <!-- Links -->
        {% include "be/include/snippets/head.html" %}        

        <!-- Extra head -->
        {% block extrahead %}{% endblock %}
        <link href="{{ contextPath }}/be/css/custom.css?{{ buildNumber }}" rel="stylesheet" type="text/css">
        <link href="{{ contextPath }}/be/css/components/statistics-dashboard.css?{{ buildNumber }}" rel="stylesheet" type="text/css">

        <script type='text/javascript'>
            window.smartlook||(function(d) {
              var o=smartlook=function(){ o.api.push(arguments)},h=d.getElementsByTagName('head')[0];
              var c=d.createElement('script');o.api=new Array();c.async=true;c.type='text/javascript';
              c.charset='utf-8';c.src='https://rec.smartlook.com/recorder.js';h.appendChild(c);
              })(document);
              smartlook('init', '7ab3e32706eb5733b649890988505b03b8ba734f');
          </script>
    </head>

    {% set sidebarClass = 'sidebar-main-hidden' %}
    {% if showSidebar %}
        {% set sidebarClass = '' %}
    {% endif %}
    <body class="navbar-bottom {{ sidebarClass }}">
        <!-- Header -->
        <div class="page-header page-header-inverse bg-success">
            <!-- Main navbar -->
            <div class="navbar navbar-inverse navbar-transparent">
                <div class="navbar-header">
                    <a class="navbar-brand" href="{{ paths('PROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C"><b>SOGENIT</b></a>
                    <ul class="nav navbar-nav pull-right visible-xs-block">
                        <li><a data-toggle="collapse" data-target="#navbar-mobile"><i class="icon-user"></i></a></li>                        
                        <li><a class="sidebar-mobile-main-toggle legitRipple"><i class="icon-menu"></i></a></li>                        
                    </ul>
                </div>
                <div class="navbar-collapse collapse" id="navbar-mobile">

                    {% if user is not empty%}
                    <div class="navbar-right">
                        <ul class="nav navbar-nav">
                            <li class="dropdown dropdown-user">
                                <a class="dropdown-toggle" data-toggle="dropdown">
                                    {% if user.imageId is not empty %}
                                        <img src="{{ paths('THUMBNAIL') }}?oid={{ user.imageId }}&scaleFactor=0.33" alt="">
                                    {% else %}
                                        <img src="https://www.miocontotermico.it/imgs/placeholder.jpg" alt="">
                                    {% endif %}                                    
                                    <span>{{ user.name is not empty ? user.name : user.fullname }} ({{ (user.credit | default(0)) }})</span>
                                    <i class="caret"></i>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-right">
                                    <li><a href="{{ paths('PROFILE') }}"><i class="icon-user"></i> Il mio profilo</a></li>
                                    {% if user.profileType == 'admin' or user.profileType == 'system' %}
                                        <li><a href="{{ paths('SMTP') }}"><i class="icon-office"></i> Dati posta</a></li>
                                    {% endif %}
                                    <li class="divider"></li>
                                    <li><a href="{{ paths('LOGOUT_DO') }}"><i class="icon-switch2"></i> Logout</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
            <!-- /main navbar -->
            <!-- Second navbar -->
            <div class="navbar navbar-inverse navbar-transparent" id="navbar-second">
                <ul class="nav navbar-nav visible-xs-block">
                    <li><a class="text-center collapsed" data-toggle="collapse" data-target="#navbar-second-toggle"><i class="icon-paragraph-justify3"></i></a></li>
                </ul>
                <div class="navbar-collapse collapse" id="navbar-second-toggle">
                    {% if user is not empty %}
                        <ul class="nav navbar-nav navbar-nav-material">      
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <i class="icon-plus22"></i> Inserisci <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu width-200">
                                    <li>
                                        <a href="{{ paths('PROCEDURES_ADD') }}" class="legitRipple">
                                            <span class=" position-left"><i class="icon-plus22"></i></span>Pratica conto termico
                                        </a>
                                    </li> 
                                    <li>
                                        <a href="{{ paths('EVALUATIONS_ADD') }}" class="legitRipple">
                                            <span class=" position-left"><i class="icon-plus22"></i></span>Valutazione sconto termico
                                        </a>
                                    </li> 
                                    <li>
                                        <a href="{{ paths('ENEAPROCEDURES_ADD') }}" class="legitRipple">
                                            <span class=" position-left"><i class="icon-plus22"></i></span>Pratica ENEA
                                        </a>
                                    </li> 
                                    <li>
                                        <a href="{{ paths('INVOICEDISCOUNTS_ADD') }}" class="legitRipple">
                                            <span class=" position-left"><i class="icon-plus22"></i></span>Pratica sconto in fattura
                                        </a>
                                    </li> 
                                </ul>
                            </li>
                            <li>
                                <a href="{{ paths('PROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C" class="legitRipple">
                                    <span class=" position-left"><i class="icon-file-text2"></i></span>Pratiche CT
                                </a>
                            </li>
                            <li>
                                <a href="{{ paths('EVALUATIONS') }}?selectedStatuses=opened%7Cprocessing%7C" class="legitRipple">
                                    <span class=" position-left"><i class="icon-meter-fast"></i></span>Valutazioni
                                </a>
                            </li> 
                            <li>
                                <a href="{{ paths('ENEAPROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C" class="legitRipple">
                                    <span class=" position-left"><i class="icon-certificate"></i></span>Pratiche ENEA
                                </a>
                            </li> 
                            <li>
                                <a href="{{ paths('INVOICEDISCOUNTS') }}?selectedStatuses=opened%7Cprocessing%7C" class="legitRipple">
                                    <span class=" position-left"><i class="icon-percent"></i></span>Pratiche Sconto FT
                                </a>
                            </li> 
                            <li>
                                <a href="{{ paths('PAPERWORKS_VIEW') }}" class="legitRipple">
                                    <span class=" position-left"><i class="icon-file-text2"></i></span>Documenti
                                </a>
                            </li> 
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    Crediti <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu width-200">
                                    <li>
                                        <a href="{{ paths('BUY_CREDIT') }}" class="legitRipple">
                                            <span class=" position-left"><i class="icon-coins"></i></span>Acquista
                                        </a>
                                    </li> 
                                    <li><a href="{{ paths('ORDERS') }}" class="legitRipple"><i class="icon-list"></i> Ordini</a></li>
                                </ul>
                            </li>
                            
                            {% if user.profileType == 'admin' or user.profileType == 'system' %}
                            <li>
                                <a href="{{ paths('USERS') }}" class="legitRipple">
                                    <span class=" position-left"><i class="icon-users"></i></span>Utenti
                                </a>
                            </li>                            
                            <li>
                                <a href="{{ paths('POSTS') }}" class="legitRipple">
                                    <span class=" position-left"><i class="icon-list"></i></span>News
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a href="{{ paths('PROFILE') }}" class="legitRipple">
                                    <span class=" position-left"><i class="icon-user"></i></span>I miei dati
                                </a>
                            </li>
                            {% if user.profileType == 'admin' or user.profileType == 'system' %}
                                <li class="dropdown">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        Sistema <span class="caret"></span>
                                    </a>
                                    <ul class="dropdown-menu width-200">
                                        <li><a href="{{ paths('SYSTEM_USERS') }}"><i class="icon-user"></i> Utenti</a></li>
                                        <li><a href="{{ paths('PAPERWORKS') }}" class="legitRipple"><i class="icon-file-text2"></i> Documenti</a></li>
                                        <li><a href="{{ paths('MAILS') }}"><i class="icon-envelop"></i> Mail</a></li>
                                        <li><a href="{{ paths('PAYMENT_PLATFORMS') }}" class="legitRipple"><i class="icon-coins"></i> Piattaforme di pagamento</a></li>
                                        <li><a href="{{ paths('PAYMENTS') }}" class="legitRipple"><i class="icon-coin-euro"></i> Pagamenti</a></li>
                                    </ul>
                                </li>

                            {% endif %}

                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}
                            <li>
                                <a href="{{ paths('STATISTICS') }}" class="legitRipple">
                                    <span class="position-left"><i class="icon-stats-bars"></i></span>Statistiche
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    {% endif %}
                </div>
            </div>
            <!-- /second navbar -->

            <!-- Sidebar controls -->
            {% block sidebarcontrol %}{% endblock %}
        </div>


        <div class="page-container">

            <!-- Content -->
            {% block content %}{% endblock %}

        </div>

        <!-- Footer -->
        {% include "be/include/snippets/footer.html" %}        
        <script>
            $.blockUI.defaults  = { 
                message:  '<img src="{{ contextPath }}/fe/imgs/loader.svg" width="64">', 
                css: { 
                    padding:        0, 
                    margin:         0, 
                    width:          '30%', 
                    top:            '40%', 
                    left:           '35%', 
                    textAlign:      'center', 
                    color:          '#0F1628', 
                    border:         'none', 
                    backgroundColor:'transparent', 
                    cursor:         'wait' 
                },
                overlayCSS:  { 
                    backgroundColor: '#000', 
                    opacity:         0.3, 
                    cursor:          'wait' 
                }, 
                baseZ: 1000, 
                showOverlay: true
            };
        </script>
        <!--Start of Tawk.to Script-->
        <script type="text/javascript">
        var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
        (function(){
        var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
        s1.async=true;
        s1.src='https://embed.tawk.to/6093991c185beb22b30a915e/1f508s63r';
        s1.charset='UTF-8';
        s1.setAttribute('crossorigin','*');
        s0.parentNode.insertBefore(s1,s0);
        })();
        </script>
        <!--End of Tawk.to Script-->


    </body>

</html>
