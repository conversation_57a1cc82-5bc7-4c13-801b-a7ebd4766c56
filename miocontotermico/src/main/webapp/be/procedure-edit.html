{% extends "be/include/base.html" %}

{% block extrahead %}
{% if property.id is not empty %}
    <title>Visualizza pratica</title>
{% else %}
    <title>Nuova pratica</title>
{% endif %}

<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Note styling -->
<style>
/* Note styling */
.note-item {
    margin-bottom: 15px;
}

.note-item .panel-heading {
    position: relative;
}

.note-item .panel-title {
    font-weight: bold;
    font-size: 14px;
}

.note-item .panel-body p {
    margin-bottom: 10px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.note-item .panel-body small {
    display: block;
    margin-top: 10px;
    border-top: 1px solid #eee;
    padding-top: 8px;
}

/* Note type specific styling */
.panel-default .panel-heading {
    background-color: #f5f5f5;
    border-color: #ddd;
}

.panel-warning .panel-heading {
    background-color: #fcf8e3;
    border-color: #faebcc;
}

.panel-danger .panel-heading {
    background-color: #f2dede;
    border-color: #ebccd1;
}

/* Button styling in notes */
.note-item .btn-xs {
    padding: 2px 6px;
    margin-left: 3px;
}

/* Modal styling */
#noteModal .modal-body {
    padding: 20px;
}

#noteModal .form-group {
    margin-bottom: 20px;
}

/* Loading spinner */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Add note button */
#add-note-btn {
    margin-bottom: 20px;
}
</style>

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/wizards/steps.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/extensions/cookie.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/procedure-edit.js?{{ buildNumber }}"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="proceduresSuccessUri" class="no-display" href="{{ paths('PROCEDURES') }}/success" rel="nofollow"></a>    
    
<!-- some hidden stuff -->
<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('PROCEDURE_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('PROCEDURE_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <!-- Editable inputs -->
                <div class="panel panel-flat">
                    <div class="panel-heading">
                        <h5 class="panel-title">{{ procedure.priority == true ? '<i class="icon-rocket"></i> <i><b>FAST</b></i> - ' : '' }}  Pratica #{{ procedure.protocol }}</h5>
                    </div>

                    <div class="tabbable">
                        <ul class="nav nav-tabs nav-tabs-highlight">
                            <li class="active"><a href="#dettagli-tab" data-toggle="tab">Dettagli</a></li>
                            {% if user.profileType == 'admin' %}
                                <li><a href="#note-tab" data-toggle="tab">Note</a></li>
                            {% endif %}
                        </ul>

                        <div class="tab-content">
                            <div class="tab-pane active" id="dettagli-tab">
                                <div class="table-responsive">
                {% if user.profileType == 'admin' or user.profileType == 'operatore' %}
                        <form id="form-edit-procedure" class="form-horizontal form-validate-jquery">
                            <table class="table table-lg">
                                <tr>
                                    <th colspan="2" class="active">Pratica assegnata a</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Utente</td>                                                                                                                                  
                                    <td>
                                        <select class="select" name="assignedUserId" id="assignedUserId">
                                            <option value=""> - </option>
                                            {% for assignedUser in userList %}
                                                {{ procedure.assignedUserId }}
                                                <option value="{{ assignedUser.id }}" {{ procedure.assignedUserId == assignedUser.id ? 'selected' : '' }}> {{ assignedUser.name}} {{ assignedUser.lastname}}</option>
                                            {% endfor %}    
                                        </select>
                                    </td>                                
                                </tr>
                                <tr>
                                    <th colspan="2" class="active">Documenti convenzione</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Convenzione finale</td>                                                                                                                                   
                                    <td>
                                        {% set listName = 'finalConventionFileIds' %}
                                        {% if procedure.finalConventionFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">                                                                
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.finalConventionFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>

                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>

                                                                    {% if user.profileType == 'admin' or user.profileType == 'operatore' %}   
                                                                        <div class="media-right media-middle">
                                                                            <ul class="icons-list">
                                                                                <li>
                                                                                    <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id}}">Rimuovi</button>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    {% endif %}
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                                                <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                                <div class="col-lg-9">
                                                    <div class="text-center">
                                                        <input type="file" name="finalConventionFileIds" data-maxfilessize="4194304" attachment="true">
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endif %}
<!--                                        <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                        <div class="text-center">
                                            <input type="file" name="finalConventionFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                        </div>-->
                                    </td>                                                                
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Convenzione da sottoscrivere</td>                                                                                                                                   
                                    <td>
                                        {% set listName = 'finalConventionToSignedIds' %}
                                        {% if procedure.finalConventionToSignedIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">                                                                
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.finalConventionToSignedIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>

                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    {% if user.profileType == 'admin' or user.profileType == 'operatore' %}   
                                                                        <div class="media-right media-middle">
                                                                            <ul class="icons-list">
                                                                                <li>
                                                                                    <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}"  procedureId="{{ procedure.id}}">Rimuovi</button>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    {% endif %}
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                                                <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                                <div class="col-lg-9">
                                                    <div class="text-center">
                                                        <input type="file" name="finalConventionToSignedIds" data-maxfilessize="4194304" attachment="true">
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if user.profileType == 'admin' %}
                                <tr>
                                    <th colspan="2" class="active">Documenti interni</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Documento interno</td>
                                    <td>
                                        {% set listName = 'internalFileIds' %}
                                        {% if procedure.internalFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.internalFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>

                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>

                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id}}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="internalFileIds" data-maxfilessize="4194304" attachment="true">
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th colspan="2" class="active">Stato pratica</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Stato</td>
                                    <td>
                                        <select class="select" name="status" id="status">
                                            <option value="opened" {{ procedure.status == 'opened' ? 'selected' : '' }}>Ricevuta</option>
                                            <option value="processing" {{ procedure.status == 'processing' ? 'selected' : '' }}>In lavorazione</option>
                                            <option value="approved" {{ procedure.status == 'approved' ? 'selected' : '' }}>Approvata</option>
                                            <option value="annulled" {{ procedure.status == 'annulled' ? 'selected' : '' }}>Annullata</option>
                                        </select>
                                    </td>
                                </tr>

                                <!-- Informazioni personali modificabili per admin -->
                                <tr>
                                    <th colspan="2" class="active">Informazioni personali</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Soggetto</td>
                                    <td>
                                        <select class="select" name="profileType" id="profileType">
                                            <option value="privato" {{ procedure.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                            <option value="azienda" {{ procedure.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Nome</td>
                                    <td>
                                        <input type="text" name="name" class="form-control maxlength" maxlength="50" value="{{ procedure.name }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Cognome</td>
                                    <td>
                                        <input type="text" name="lastname" class="form-control maxlength" maxlength="50" value="{{ procedure.lastname }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Ragione sociale</td>
                                    <td>
                                        <input type="text" name="fullname" class="form-control maxlength" maxlength="100" value="{{ procedure.fullname }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Codice fiscale</td>
                                    <td>
                                        <input type="text" name="tin" class="form-control maxlength" maxlength="16" value="{{ procedure.tin }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">P.IVA</td>
                                    <td>
                                        <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" value="{{ procedure.vatNumber }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Email</td>
                                    <td>
                                        <input type="email" name="email" class="form-control maxlength" maxlength="100" value="{{ procedure.email }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">PEC</td>
                                    <td>
                                        <input type="email" name="pec" class="form-control maxlength" maxlength="100" value="{{ procedure.pec }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Indirizzo, città, provincia, cap</td>
                                    <td>
                                        <input type="text" name="address" class="form-control maxlength" maxlength="200" value="{{ procedure.address }} {{ procedure.city }} ({{ procedure.provinceCode }}) - {{ decode('country', procedure.countryCode ) }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Telefono</td>
                                    <td>
                                        <input type="text" name="phoneNumber" class="form-control maxlength" maxlength="20" value="{{ procedure.phoneNumber }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">IBAN</td>
                                    <td>
                                        <input type="text" name="iban" class="form-control maxlength" maxlength="34" value="{{ procedure.iban }}">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Data ultimo pagamento</td>
                                    <td>
                                        <input type="text" name="lastPaymentDate" class="form-control daterange-single" value="{{ procedure.lastPaymentDate | date('dd/MM/yyyy') }}" placeholder="dd/mm/yyyy">
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Coupon</td>
                                    <td>
                                        <input type="text" name="coupon" class="form-control maxlength" maxlength="50" value="{{ procedure.coupon }}">
                                    </td>
                                </tr>

                                <!-- Titolarità immobile -->
                                <tr>
                                    <th colspan="2" class="active">Titolarità immobile</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Titolo di possesso</td>
                                    <td>
                                        <select class="select" name="ownership">
                                            <option value="owner" {{ procedure.ownership == 'owner' ? 'selected' : '' }}>Proprietario o comproprietario</option>
                                            <option value="tenant" {{ procedure.ownership == 'tenant' ? 'selected' : '' }}>Detentore\Utilizzatore</option>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">PIN GWA/GSE</td>
                                    <td>
                                        <input type="text" name="pin" class="form-control maxlength" maxlength="50" value="{{ procedure.pin }}">
                                    </td>
                                </tr>
                            </table>

                            <!-- Sezione gestione documenti per admin -->
                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}
                            <table class="table table-lg">
                                <tr>
                                    <th colspan="2" class="active">Documenti caricati</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Delega ad operare nel PortalTermico:</td>
                                    <td>
                                        {% set listName = 'thermalPortalDelegationFileIds' %}
                                        {% if procedure.thermalPortalDelegationFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.thermalPortalDelegationFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="thermalPortalDelegationFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Documento d'identità:</td>
                                    <td>
                                        {% set listName = 'identityDocumentFileIds' %}
                                        {% if procedure.identityDocumentFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.identityDocumentFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="identityDocumentFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Codice fiscale:</td>
                                    <td>
                                        {% set listName = 'tinFileIds' %}
                                        {% if procedure.tinFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.tinFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="tinFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Modulo di autorizzazione del proprietario dell'immobile:</td>
                                    <td>
                                        {% set listName = 'authorizationFormFileIds' %}
                                        {% if procedure.authorizationFormFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.authorizationFormFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="authorizationFormFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Documento d'identità del proprietario dell'immobile:</td>
                                    <td>
                                        {% set listName = 'identityDocumentOwnerFormFileIds' %}
                                        {% if procedure.identityDocumentOwnerFormFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.identityDocumentOwnerFormFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="identityDocumentOwnerFormFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Fatture e bonifici inerenti all'intervento:</td>
                                    <td>
                                        {% set listName = 'invoiceFileIds' %}
                                        {% if procedure.invoiceFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.invoiceFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="invoiceFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Contratto di finanziamento:</td>
                                    <td>
                                        {% set listName = 'contractFileIds' %}
                                        {% if procedure.contractFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.contractFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="contractFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Scheda raccolta dati:</td>
                                    <td>
                                        {% set listName = 'dataCollectionFormFileIds' %}
                                        {% if procedure.dataCollectionFormFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.dataCollectionFormFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="dataCollectionFormFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Scheda tecnica e certificato ambientale:</td>
                                    <td>
                                        {% set listName = 'technicalSheetFileIds' %}
                                        {% if procedure.technicalSheetFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.technicalSheetFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="technicalSheetFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Documento di Smaltimento:</td>
                                    <td>
                                        {% set listName = 'disposalDocumentFileIds' %}
                                        {% if procedure.disposalDocumentFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.disposalDocumentFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="disposalDocumentFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Mandato all'incasso</td>
                                    <td>
                                        {% set listName = 'identityDocumentAgentFileIds' %}
                                        {% if procedure.identityDocumentAgentFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.identityDocumentAgentFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="identityDocumentAgentFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Documento d'identità mandatario</td>
                                    <td>
                                        {% set listName = 'cashingMandateFileIds' %}
                                        {% if procedure.cashingMandateFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.cashingMandateFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="cashingMandateFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>

                            <!-- Sezione documenti tecnici impianti -->
                            <table class="table table-lg">
                                <tr>
                                    <th colspan="2" class="active">Documenti tecnici impianti</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori):</td>
                                    <td>
                                        {% set listName = 'plate1FileIds' %}
                                        {% if procedure.plate1FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.plate1FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="plate1FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio da distante e da vicino dei generatori interni sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'thermalPlant1FileIds' %}
                                        {% if procedure.thermalPlant1FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.thermalPlant1FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="thermalPlant1FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio da distante e da vicino dei generatori esterni sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'generator1FileIds' %}
                                        {% if procedure.generator1FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.generator1FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="generator1FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata:</td>
                                    <td>
                                        {% set listName = 'valves1FileIds' %}
                                        {% if procedure.valves1FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.valves1FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="valves1FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori):</td>
                                    <td>
                                        {% set listName = 'plate2FileIds' %}
                                        {% if procedure.plate2FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.plate2FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="plate2FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio dei collegamenti (canna fumaria e/o collegamenti idraulici) dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'thermal2PlantFileIds' %}
                                        {% if procedure.thermal2PlantFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.thermal2PlantFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="thermal2PlantFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio da distante e da vicino dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'generator2FileIds' %}
                                        {% if procedure.generator2FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.generator2FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="generator2FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata:</td>
                                    <td>
                                        {% set listName = 'valves2FileIds' %}
                                        {% if procedure.valves2FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.valves2FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="valves2FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme del sistema di accumulo termico installato e relativa targa, dove previsto:</td>
                                    <td>
                                        {% set listName = 'globalStorage2FileIds' %}
                                        {% if procedure.globalStorage2FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.globalStorage2FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="globalStorage2FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio del pannello solare installato:</td>
                                    <td>
                                        {% set listName = 'detailPanel3FileIds' %}
                                        {% if procedure.detailPanel3FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.detailPanel3FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="detailPanel3FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio della targa dei collettori solari installati:</td>
                                    <td>
                                        {% set listName = 'detailPlate3FileIds' %}
                                        {% if procedure.detailPlate3FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.detailPlate3FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="detailPlate3FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio del bollitore:</td>
                                    <td>
                                        {% set listName = 'detailBoiler3FileIds' %}
                                        {% if procedure.detailBoiler3FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.detailBoiler3FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="detailBoiler3FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme del campo solare in fase di installazione:</td>
                                    <td>
                                        {% set listName = 'globalInstalling3FieldFileIds' %}
                                        {% if procedure.globalInstalling3FieldFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.globalInstalling3FieldFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="globalInstalling3FieldFileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme del campo solare realizzato:</td>
                                    <td>
                                        {% set listName = 'globalField3FileIds' %}
                                        {% if procedure.globalField3FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.globalField3FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="globalField3FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata, ove previste:</td>
                                    <td>
                                        {% set listName = 'valves3FileIds' %}
                                        {% if procedure.valves3FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.valves3FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="valves3FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'detailGenerator4FileIds' %}
                                        {% if procedure.detailGenerator4FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.detailGenerator4FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="detailGenerator4FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'globalGenerator4FileIds' %}
                                        {% if procedure.globalGenerator4FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.globalGenerator4FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="globalGenerator4FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Targa dei generatori installati:</td>
                                    <td>
                                        {% set listName = 'plate4FileIds' %}
                                        {% if procedure.plate4FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.plate4FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="plate4FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori):</td>
                                    <td>
                                        {% set listName = 'plate5FileIds' %}
                                        {% if procedure.plate5FileIds is not empty %}
                                        <div class="col-lg-9">
                                            <div class="panel panel-flat">
                                                <div class="panel-body">
                                                    <ul class="media-list">
                                                        {% for fileId in procedure.plate5FileIds %}
                                                        <li class="media">
                                                            <div class="media-left">
                                                                <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                            </div>
                                                            <div class="media-body">
                                                                <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                    {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                </a>
                                                                <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                            </div>
                                                            <div class="media-right media-middle">
                                                                <ul class="icons-list">
                                                                    <li>
                                                                        <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="col-lg-9">
                                            <div class="text-center">
                                                <input type="file" name="plate5FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Generatori sostituiti e installati:</td>
                                    <td>
                                        {% set listName = 'generator5FileIds' %}
                                        {% if procedure.generator5FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.generator5FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="generator5FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Centrale termica, o il locale di installazione, ante-operam (presente il generatore sostituito) e post-operam (presente il generatore installato):</td>
                                    <td>
                                        {% set listName = 'thermalPlant5FileIds' %}
                                        {% if procedure.thermalPlant5FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.thermalPlant5FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="thermalPlant5FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata:</td>
                                    <td>
                                        {% set listName = 'valves5FileIds' %}
                                        {% if procedure.valves5FileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">
                                                        <ul class="media-list">
                                                            {% for fileId in procedure.valves5FileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>
                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>
                                                                    <div class="media-right media-middle">
                                                                        <ul class="icons-list">
                                                                            <li>
                                                                                <button type="button" href="{{ paths('PROCEDURES_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" procedureId="{{ procedure.id }}">Rimuovi</button>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="col-lg-9">
                                                <div class="text-center">
                                                    <input type="file" name="valves5FileIds" data-maxfilessize="4194304" attachment="true" multiple>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                            {% endif %}

                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}
                                <div class="panel-footer has-visible-elements">
                                    <div class="heading-elements visible-elements">
                                        <span class="heading-text text-semibold">Azioni:</span>
                                        <div class="pull-right">
                                            <button type="button" class="procedureSave btn bg-success-600" href="{{ paths('PROCEDURE_EDIT_SAVE') }}?procedureId={{ procedure.id }}">Salva <i class="icon-arrow-right14"></i></button>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </form>
                    </div>
                {% else %}
                        <table class="table table-lg">

                            <tr>
                                <th colspan="2" class="active">Informazioni personali</th>
                            </tr>
                            <tr>

                                <td style="width: 33%;">Soggetto</td>
                                {% if procedure.profileType == 'privato' %}
                                    <td><span class="label bg-info">{{ procedure.profileType }}</span></td>
                                {% elseif procedure.profileType == 'azienda'  %}
                                    <td><span class="label bg-warning">{{ procedure.profileType }}</span></td>
                                {% endif %}
                            </tr>
                            {% if procedure.profileType == 'privato' %}
                                <tr>
                                    <td style="width: 33%;">Nome</td>
                                    <td>{{ procedure.name }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Cognome</td>
                                    <td>{{ procedure.lastname }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Codice fiscale</td>
                                    <td>{{ procedure.tin }}</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td style="width: 33%;">Indirizzo, città, provincia, cap</td>
                                <td>{{ procedure.address }} {{ procedure.city }} ({{ procedure.provinceCode }}) - {{ decode('country', procedure.countryCode ) }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Email</td>
                                <td>{{ procedure.email }}</td>
                            </tr>
                            {% if procedure.profileType == 'azienda' %}
                                <tr>
                                    <td style="width: 33%;">PEC</td>
                                    <td>{{ procedure.pec }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Ragione sociale</td>
                                    {% set fullname = '*Nome* *Cognome*' %}
                                    {% if procedure.fullname is not empty %}
                                        {% set fullname = procedure.fullname %}
                                    {% elseif (procedure.name is not empty) and (procedure.lastname is not empty) %}
                                        {% set fullname = (procedure.name | default('*Nome*')) + ' ' + procedure.lastname | default('*Cognome*') %}
                                    {% endif %}
                                    <td>{{ fullname }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">P.IVA</td>
                                    <td>{{ procedure.vatNumber }}</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td style="width: 33%;">Telefono</td>
                                <td>{{ procedure.phoneNumber }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">IBAN</td>
                                <td>{{ procedure.iban }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Data ultimo pagamento</td>
                                <td>{{ procedure.lastPaymentDate | date('dd/MM/yyyy') }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Coupon</td>
                                <td>{{ procedure.coupon }}</td>
                            </tr>
                            <tr>
                                <th colspan="2" class="active">Titolarità immobile</th>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Titolo di possesso</td>
                                {% if procedure.ownership == 'owner' %}
                                    <td>Proprietario o comproprietario</td>
                                {% else %}
                                    <td>Detentore\Utilizzatore</td>
                                {% endif %}
                            </tr>
                            <tr>
                                <td style="width: 33%;">PIN GWA/GSE</td>
                                <td>{{ procedure.pin }}</td>
                            </tr>
                            <tr>
                                <th colspan="2" class="active">Documenti caricati <a href="{{ paths('FILEZIP') + '?procedureId=' + procedure.id }}" download>Scarica tutti</a></th>
                            </tr>
                            {% if procedure.thermalPortalDelegationFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Delega ad operare nel PortalTermico:</td>
                                    <td>
                                        {% for fileId in procedure.thermalPortalDelegationFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.identityDocumentFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Documento d'identità:</td>
                                    <td>
                                        {% for fileId in procedure.identityDocumentFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.tinFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Codice fiscale:</td>
                                    <td>
                                        {% for fileId in procedure.tinFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.authorizationFormFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Modulo di autorizzazione del proprietario dell'immobile:</td>
                                    <td>
                                        {% for fileId in procedure.authorizationFormFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.identityDocumentOwnerFormFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Documento d'identità del proprietario dell'immobile:</td>
                                    <td>
                                        {% for fileId in procedure.identityDocumentOwnerFormFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.invoiceFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Fatture e bonifici inerenti all'intervento:</td>
                                    <td>
                                        {% for fileId in procedure.invoiceFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.contractFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Contratto di finanziamento:</td>
                                    <td>
                                        {% for fileId in procedure.contractFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.dataCollectionFormFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Scheda raccolta dati:</td>
                                    <td>
                                        {% for fileId in procedure.dataCollectionFormFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.technicalSheetFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Scheda tecnica e certificato ambientale:</td>
                                    <td>
                                        {% for fileId in procedure.technicalSheetFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.disposalDocumentFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Documento di Smaltimento:</td>
                                    <td>
                                        {% for fileId in procedure.disposalDocumentFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.identityDocumentAgentFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Mandato all’incasso:</td>
                                    <td>
                                        {% for fileId in procedure.identityDocumentAgentFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.cashingMandateFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Documento d’identità mandatario:</td>
                                    <td>
                                        {% for fileId in procedure.cashingMandateFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}


                            {% if procedure.plate1FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori):</td>
                                    <td>
                                        {% for fileId in procedure.plate1FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.thermalPlant1FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio da distante e da vicino dei generatori interni sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.thermalPlant1FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.generator1FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio da distante e da vicino dei generatori esterni sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.generator1FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.valves1FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata:</td>
                                    <td>
                                        {% for fileId in procedure.valves1FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}

                            {% if procedure.plate2FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori):</td>
                                    <td>
                                        {% for fileId in procedure.plate2FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.thermal2PlantFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio dei collegamenti (canna fumaria e/o collegamenti idraulici) dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.thermal2PlantFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}">Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.generator2FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio da distante e da vicino dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.generator2FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.valves2FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata:</td>
                                    <td>
                                        {% for fileId in procedure.valves2FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.globalStorage2FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme del sistema di accumulo termico installato e relativa targa, dove previsto:</td>
                                    <td>
                                        {% for fileId in procedure.globalStorage2FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}

                            {% if procedure.detailPanel3FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio del pannello solare installato:</td>
                                    <td>
                                        {% for fileId in procedure.detailPanel3FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.detailPlate3FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio della targa dei collettori solari installati:</td>
                                    <td>
                                        {% for fileId in procedure.detailPlate3FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.detailBoiler3FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio del bollitore:</td>
                                    <td>
                                        {% for fileId in procedure.detailBoiler3FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.globalInstalling3FieldFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme del campo solare in fase di installazione:</td>
                                    <td>
                                        {% for fileId in procedure.globalInstalling3FieldFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.globalField3FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme del campo solare realizzato:</td>
                                    <td>
                                        {% for fileId in procedure.globalField3FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.valves3FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata, ove previste:</td>
                                    <td>
                                        {% for fileId in procedure.valves3FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.detailGenerator4FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista di dettaglio dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.detailGenerator4FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.globalGenerator4FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Vista d'insieme dei generatori sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.globalGenerator4FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.plate4FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Targa dei generatori installati:</td>
                                    <td>
                                        {% for fileId in procedure.plate4FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.plate5FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori):</td>
                                    <td>
                                        {% for fileId in procedure.plate5FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.generator5FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Generatori sostituiti e installati:</td>
                                    <td>
                                        {% for fileId in procedure.generator5FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.thermalPlant5FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Centrale termica, o il locale di installazione, ante-operam (presente il generatore sostituito) e post-operam (presente il generatore installato):</td>
                                    <td>
                                        {% for fileId in procedure.thermalPlant5FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if procedure.valves5FileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Valvole termostatiche o del sistema di regolazione modulante della portata:</td>
                                    <td>
                                        {% for fileId in procedure.valves5FileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            <!-- ecc ecc -->
                        </table>
                    </div>
                {% endif %}
                            </div>

                            {% if user.profileType == 'admin' %}
                            <div class="tab-pane" id="note-tab">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <button type="button" class="btn btn-primary mb-3" id="add-note-btn">
                                                <i class="icon-plus-circle2"></i> Aggiungi Nota
                                            </button>
                                        </div>
                                    </div>
                                    <div id="notes-container">
                                        <div class="text-center">
                                            <i class="icon-spinner2 spinner"></i> Caricamento note...
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<!-- Note Modal -->
{% if user.profileType == 'admin' %}
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="noteModalTitle">Aggiungi Nota</h4>
            </div>
            <div class="modal-body">
                <form id="noteForm">
                    <input type="hidden" id="noteId" name="noteId">
                    <div class="form-group">
                        <label for="noteType">Tipologia:</label>
                        <select class="form-control" id="noteType" name="type" required>
                            <option value="">Seleziona tipologia</option>
                            <option value="semplice">Semplice</option>
                            <option value="attenzione">Attenzione</option>
                            <option value="problema">Problema</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="noteContent">Contenuto:</label>
                        <textarea class="form-control" id="noteContent" name="content" rows="5" required placeholder="Inserisci il contenuto della nota..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Annulla</button>
                <button type="button" class="btn btn-primary" id="saveNoteBtn">Salva</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
    // Initialize daterangepicker for single date selection
    $('.daterange-single').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Applica',
            cancelLabel: 'Annulla',
            fromLabel: 'Da',
            toLabel: 'A',
            customRangeLabel: 'Personalizzato',
            weekLabel: 'S',
            daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
            monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
            firstDay: 1
        }
    });

    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-success',
                    action: function () {
                        window.location.href = "{{ paths('PROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C";
                    }
                }
            }
        });
    });
</script>

{% endblock %}
