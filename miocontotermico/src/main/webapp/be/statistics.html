{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Statistiche Pratiche</title>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
    <script src="{{ contextPath }}/be/js/components/statistics-dashboard.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('STATISTICS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Operazione completata con successo</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('STATISTICS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante l'operazione. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Statistics Dashboard -->
        <div class="panel panel-flat">
            <div class="panel-heading">
                <h5 class="panel-title">Dashboard Statistiche</h5>
                <div class="heading-elements">
                    <ul class="icons-list">
                        <li><a data-action="collapse"></a></li>
                        <li><a data-action="reload" id="dashboard-reload"></a></li>
                    </ul>
                </div>
            </div>

            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div id="statistics-dashboard-container"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /statistics dashboard -->

        <!-- Additional Statistics Panels (Future Enhancement) -->
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-flat">
                    <div class="panel-heading">
                        <h6 class="panel-title">Statistiche Rapide</h6>
                    </div>
                    <div class="panel-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="content-group-sm">
                                    <h5 class="text-semibold no-margin">
                                        <span id="total-procedures-month">-</span>
                                    </h5>
                                    <span class="text-muted text-size-small">Pratiche questo mese</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="content-group-sm">
                                    <h5 class="text-semibold no-margin">
                                        <span id="avg-procedures-user">-</span>
                                    </h5>
                                    <span class="text-muted text-size-small">Media per utente</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="content-group-sm">
                                    <h5 class="text-semibold no-margin">
                                        <span id="active-users">-</span>
                                    </h5>
                                    <span class="text-muted text-size-small">Utenti attivi</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="panel panel-flat">
                    <div class="panel-heading">
                        <h6 class="panel-title">Informazioni</h6>
                    </div>
                    <div class="panel-body">
                        <div class="content-group">
                            <h6 class="text-semibold">Come utilizzare le statistiche</h6>
                            <ul class="list-unstyled">
                                <li><i class="icon-arrow-right8 position-left text-muted"></i> Seleziona il mese e l'anno desiderati</li>
                                <li><i class="icon-arrow-right8 position-left text-muted"></i> Il grafico mostra le pratiche assegnate per utente</li>
                                <li><i class="icon-arrow-right8 position-left text-muted"></i> I dati vengono aggiornati automaticamente</li>
                                <li><i class="icon-arrow-right8 position-left text-muted"></i> Usa il pulsante ricarica per aggiornare manualmente</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /additional panels -->

    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<script>
$(document).ready(function() {
    // Initialize statistics dashboard
    var dashboard = new StatisticsDashboard(
        'statistics-dashboard-container',
        '{{ paths("DATA_STATISTICS_PROCEDURES_BY_USER") }}',
        { pageMode: true }
    );
    
    // Handle reload button
    $('#dashboard-reload').on('click', function(e) {
        e.preventDefault();
        if (dashboard && typeof dashboard.loadData === 'function') {
            dashboard.loadData();
        }
    });
    
    // Update quick stats when dashboard data changes
    // This will be enhanced in future versions
    function updateQuickStats(data) {
        console.log('Updating quick stats:', data);
        if (data && data.statistics) {
            var total = data.statistics.reduce(function(sum, stat) {
                return sum + (stat.count || 0);
            }, 0);
            
            var activeUsers = data.statistics.length;
            var avgPerUser = activeUsers > 0 ? Math.round(total / activeUsers * 10) / 10 : 0;
            
            $('#total-procedures-month').text(total);
            $('#avg-procedures-user').text(avgPerUser);
            $('#active-users').text(activeUsers);
        }
    }
    
    // Hook into dashboard data loading (if available)
    if (dashboard) {
        dashboard.onDataLoaded = updateQuickStats;
    }
});
</script>

{% endblock %}
